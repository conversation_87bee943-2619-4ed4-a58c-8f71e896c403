package controllers

import (
	"context"
	"log/slog"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type RenditionWriteController struct {
	logger  *slog.Logger
	maker   *usecases.RenditionCreator
	saver   *usecases.RenditionSaver
	updater *usecases.RenditionUpdater
}

func NewRenditionWriteController(logger *slog.Logger, maker *usecases.RenditionCreator, saver *usecases.RenditionSaver, updater *usecases.RenditionUpdater) *RenditionWriteController {
	if usecases.IsNil(maker) {
		panic("maker cannot be nil")
	}
	if usecases.IsNil(saver) {
		panic("saver cannot be nil")
	}
	if usecases.IsNil(updater) {
		panic("updater cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &RenditionWriteController{logger: logger, maker: maker, saver: saver, updater: updater}
}

func (r *RenditionWriteController) AddRendition(ctx context.Context, designId uuid.UUID, rendition adapters.Rendition, presenter usecases.RenditionCreationOutcomePresenter) {
	usecaseRendition, err := rendition.ToDomain()
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to convert rendition to usecase", slog.String("error", err.Error()))
		presenter.PresentError(err)
		return
	}
	r.maker.Create(ctx, designId, usecaseRendition, presenter)
}
func (r *RenditionWriteController) SaveRendition(ctx context.Context, designId uuid.UUID, rendition adapters.Rendition, presenter usecases.OutcomePresenter) {
	usecaseRendition, err := rendition.ToDomain()
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to convert rendition to usecase", slog.String("error", err.Error()))
		presenter.PresentError(err)
		return
	}
	r.saver.Save(ctx, designId, usecaseRendition, presenter)
}

func (r *RenditionWriteController) UpdateRendition(ctx context.Context, rendition adapters.Rendition, presenter usecases.OutcomePresenter) {
	diff, err := rendition.ToDiff()
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to convert rendition to diff", slog.String("error", err.Error()))
		presenter.PresentError(err)
		return
	}
	r.updater.UpdateRendition(ctx, presenter, diff)
}
