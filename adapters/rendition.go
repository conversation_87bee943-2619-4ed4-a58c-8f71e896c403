package adapters

import (
	"errors"
	"fmt"
	"net/url"
	"time"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type Rendition struct {
	Id        uuid.UUID `json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	Status entities.RenditionStatus `json:"status"`
	URL    string                   `json:"url"`
}

func (r *Rendition) AlignId(id string) error {
	renditionId, err := uuid.Parse(id)
	if err != nil {
		return fmt.Errorf("invalid rendition UUID %s: %w", id, err)
	}
	zeroUUID := uuid.UUID{}
	if r.Id == zeroUUID {
		r.Id = renditionId
		return nil
	}
	if r.Id != renditionId {
		return errors.New("rendition ID mismatch")
	}
	return nil
}

func (r *Rendition) ToDomain() (entities.Rendition, error) {
	url, err := url.Parse(r.URL)
	if err != nil {
		return entities.Rendition{}, err
	}
	return entities.Rendition{
		Id:        r.Id,
		CreatedAt: r.Created<PERSON>t,
		UpdatedAt: r.UpdatedAt,
		Status:    r.Status,
		URL:       *url,
	}, nil
}

func (r *Rendition) ToDiff() (entities.RenditionDiff, error) {
	url, err := url.Parse(r.URL)
	if err != nil {
		return entities.RenditionDiff{}, err
	}
	return entities.RenditionDiff{
		Id:     r.Id,
		Status: r.Status,
		URL:    url,
	}, nil
}

func FromDomainRendition(r entities.Rendition) Rendition {
	return Rendition{
		Id:        r.Id,
		CreatedAt: r.CreatedAt,
		UpdatedAt: r.UpdatedAt,
		Status:    r.Status,
		URL:       r.URL.String(),
	}
}
