package presenters

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

const defaultMeasurements = `{
  "ceilingArea": 64.89221,
  "floorArea": 50.3027878,
  "halfWallTileLength": 306.588379,
  "linearLengthOfWall": 276.567932,
  "nichesArea": 10.4444237,
  "showerAreaHeight": 75.66934,
  "showerWallArea": 58.05525,
  "tubLength": 58.36333,
  "vanityHalfWallArea": 36.19948,
  "vanityLength": 78.5976,
  "vanityWallArea": 82.4565048,
  "vanityWallLength": 124.112495,
  "wallHalfArea": 89.42161,
  "wallPaintArea": 203.687836
}`

type PresetPresenter struct {
	DataPresenter
	defaultRoomLayout json.RawMessage
}

func NewPresetPresenter(w http.ResponseWriter, logger *slog.Logger, defaultRoomLayout json.RawMessage) PresetPresenter {
	return PresetPresenter{
		DataPresenter:     NewDataPresenter(logger, w),
		defaultRoomLayout: defaultRoomLayout,
	}
}

func (pp PresetPresenter) PresentPreset(ctx context.Context, preset usecases.Preset) {
	if preset.RoomLayout.RawData == nil {
		preset.RoomLayout.RawData = pp.defaultRoomLayout
	}
	preset.Measurements = json.RawMessage(defaultMeasurements)
	p := adapters.FromUsecasePreset(preset)
	pp.w.Header().Set("Location", fmt.Sprintf("/presets/%s", preset.Id))
	pp.PresentData(ctx, p)
}
