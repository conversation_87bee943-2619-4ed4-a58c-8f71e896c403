package presenters

import (
	"context"
	"fmt"
	"log/slog"
	"net/http"

	"github.com/google/uuid"
	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type RenditionsPresenter struct {
	DataPresenter
}

func NewRenditionsPresenter(logger *slog.Logger, w http.ResponseWriter) *RenditionsPresenter {
	return &RenditionsPresenter{
		DataPresenter: NewDataPresenter(logger, w),
	}
}
func (rp *RenditionsPresenter) PresentRenditions(ctx context.Context, renditions []entities.Rendition) {
	output := make([]adapters.Rendition, len(renditions))
	for i, r := range renditions {
		output[i] = adapters.FromDomainRendition(r)
	}
	rp.PresentData(ctx, output)
}

type RenditionCreationOutcomePresenter struct {
	OutcomePresenter
	designId uuid.UUID
}

func NewRenditionCreationOutcomePresenter(logger *slog.Logger, w http.ResponseWriter, designId uuid.UUID) *RenditionCreationOutcomePresenter {
	return &RenditionCreationOutcomePresenter{OutcomePresenter: OutcomePresenter{logger: logger, w: w}, designId: designId}
}

func (p *RenditionCreationOutcomePresenter) ConveySuccessWithResourceLocation(rendition entities.Rendition) {
	p.w.Header().Set("Access-Control-Allow-Origin", "*")
	var zeroUUID uuid.UUID
	if rendition.Id == uuid.Nil || rendition.Id == zeroUUID {
		http.Error(p.w, "Missing/invalid ID in created rendition", http.StatusInternalServerError)
		return
	}
	p.w.Header().Set("Location", fmt.Sprintf("/designs/%s/renditions/%s", p.designId, rendition.Id))
	p.w.WriteHeader(http.StatusCreated)
}
