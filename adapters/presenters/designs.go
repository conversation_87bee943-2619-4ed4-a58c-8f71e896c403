package presenters

import (
	"context"
	"encoding/json"
	"log/slog"
	"net/http"
	"unicode"

	"github.com/kodeart/go-problem/v2"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type DesignsPresenter struct {
	DataPresenter
}

func NewDesignsPresenter(logger *slog.Logger, w http.ResponseWriter) *DesignsPresenter {
	return &DesignsPresenter{
		DataPresenter: NewDataPresenter(logger, w),
	}
}

func (dp *DesignsPresenter) PresentDesign(ctx context.Context, design usecases.Design) {
	d := adapters.FromUsecaseDesign(design)
	dp.PresentData(ctx, d)
}
func (dp *DesignsPresenter) PresentDesigns(ctx context.Context, designs []usecases.Design) {
	output := make([]adapters.Design, len(designs))
	for i, d := range designs {
		output[i] = adapters.FromUsecaseDesign(d)
	}
	dp.PresentData(ctx, output)
}

type MultiProjectOutputItem struct {
	Status int                `json:"status"`
	Data   []adapters.Design  `json:"data"`
	Errors []*problem.Problem `json:"errors,omitempty"`
}

func (dp *DesignsPresenter) PresentDesignsByProject(ctx context.Context, data map[entities.ProjectId][]usecases.Design, errors []error) {
	results := make(map[entities.ProjectId]MultiProjectOutputItem)
	for projectId, designs := range data {
		output := make([]adapters.Design, len(designs))
		for i, d := range designs {
			output[i] = adapters.FromUsecaseDesign(d)
		}
		results[projectId] = MultiProjectOutputItem{
			Status: http.StatusOK,
			Data:   output,
		}
	}
	if len(errors) > 0 {
		var errList []*problem.Problem
		for _, err := range errors {
			errList = append(errList, problem.New().WithStatus(http.StatusNotFound).WithDetail(err.Error()))
		}
		results["errors"] = MultiProjectOutputItem{
			Status: http.StatusNotFound,
			Errors: errList,
		}
	}
	dp.w.Header().Set("Access-Control-Allow-Origin", "*")
	bytes, err := json.Marshal(results)
	if err != nil {
		dp.logger.ErrorContext(ctx, "Could not marshal design data into JSON",
			slog.String("error", err.Error()))
		http.Error(dp.w, err.Error(), http.StatusInternalServerError)
		return
	}
	dp.w.Header().Set("Content-Type", "application/json")
	dp.w.WriteHeader(http.StatusMultiStatus)
	if _, err := dp.w.Write(bytes); err != nil {
		http.Error(dp.w, err.Error(), http.StatusInternalServerError)
	}
}

func UsePascalCaseTilePatterns(design adapters.Design) adapters.Design {
	design.FloorTilePattern = capitalizeFirstLetter(design.FloorTilePattern)
	design.ShowerFloorTilePattern = capitalizeFirstLetter(design.ShowerFloorTilePattern)
	design.ShowerWallTilePattern = capitalizeFirstLetter(design.ShowerWallTilePattern)
	design.WallTilePattern = capitalizeFirstLetter(design.WallTilePattern)
	return design
}

func capitalizeFirstLetter(tp *usecases.TilePattern) *usecases.TilePattern {
	if tp == nil || len(*tp) == 0 {
		return tp
	}
	runes := []rune(*tp)
	runes[0] = unicode.ToTitle(runes[0])
	result := usecases.TilePattern(runes)
	return &result
}
