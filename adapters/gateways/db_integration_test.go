//go:build integration

package gateways_test

import (
	"context"
	"database/sql"
	"log"
	"net/url"
	"os"
	"testing"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

const TEST_DATABASE_URL = "postgres://postgres@localhost:5432/projects"

var testPool *pgxpool.Pool // Global pool for all tests in this package

// TestMain is the entry point for tests in this package. It sets up the
// database connection pool and closes it after all tests have run.
func TestMain(m *testing.M) {
	databaseUrl := os.Getenv("TEST_DATABASE_URL")
	if databaseUrl == "" {
		log.Println("TEST_DATABASE_URL env var not set; using default value: ", TEST_DATABASE_URL)
		databaseUrl = TEST_DATABASE_URL
	}

	var err error
	testPool, err = pgxpool.New(context.Background(), databaseUrl)
	if err != nil {
		log.Fatalf("Failed to connect to test database: %v", err)
	}
	defer testPool.Close()

	// Run all tests in the package
	exitCode := m.Run()
	os.Exit(exitCode)
}

// TestRelationalDb_Integration_Designs_CRUD performs a full create, read, update, and delete
// cycle against a live database to ensure the entire lifecycle works as expected.
func TestRelationalDb_Integration_Designs_CRUD(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool)

	// Ensure the database is clean before this test runs.
	// CASCADE is crucial to also truncate tables with foreign keys.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate tables before test")

	testDesign := usecases.Design{
		ProjectID:          "PRJ-TEST123",
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			Title:            sql.NullString{String: "Integration Test", Valid: true},
			Description:      sql.NullString{Valid: false},
			ColorScheme:      ptr(usecases.Neutral),
			Style:            ptr(usecases.Modern),
			FloorTilePattern: ptr(usecases.Herringbone),
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile:      ptr(uuid.New()),
				Lighting:       ptr(uuid.New()),
				Mirror:         ptr(uuid.New()),
				Paint:          ptr(uuid.New()),
				ShowerWallTile: &uuid.Nil,
				Toilet:         ptr(uuid.New()),
			},
			Faucet:      ptr(uuid.New()),
			ShowerGlass: &uuid.Nil,
			TubDoor:     &uuid.Nil,
			Vanity:      ptr(uuid.New()),
		},
	}
	*testDesign.ShowerWallTile = uuid.New()
	*testDesign.ShowerGlass = uuid.New()
	*testDesign.TubDoor = uuid.New()

	// --- 1. Test Create ---
	createdID, err := db.UpsertDesign(ctx, testDesign)
	require.NoError(t, err, "Create failed")
	require.NotEqual(t, uuid.Nil, createdID, "Create should return a valid UUID")

	// --- 2. Test Read ---
	readDesign, err := db.ReadDesign(ctx, createdID)
	require.NoError(t, err, "Read failed after create")
	require.NotNil(t, readDesign)
	require.NotNil(t, readDesign.Created)
	require.NotEmpty(t, readDesign.ID)

	assert.Equal(t, createdID, readDesign.ID)
	assert.Equal(t, testDesign.ProjectID, readDesign.ProjectID)
	assert.Equal(t, testDesign.Title.String, readDesign.Title.String)
	assert.Equal(t, usecases.Herringbone, *readDesign.FloorTilePattern)
	assert.Equal(t, usecases.Modern, *readDesign.Style)
	assert.Equal(t, usecases.Neutral, *readDesign.ColorScheme)
	assert.Equal(t, usecases.Preview, readDesign.Status)
	assert.Equal(t, readDesign.Created, readDesign.LastUpdated)

	// --- 3. Test Update ---
	readDesign.Title = sql.NullString{String: "Updated Title", Valid: true}
	readDesign.FloorTilePattern = ptr(usecases.VerticalStacked)
	readDesign.Style = ptr(usecases.Traditional)
	readDesign.ColorScheme = ptr(usecases.Bold)
	readDesign.Status = usecases.Archived
	readDesign.TubDoorVisible = true
	readDesign.NichesVisible = true

	_, err = db.UpsertDesign(ctx, readDesign)
	require.NoError(t, err, "Update failed")

	// --- 4. Read again to verify update ---
	updatedDesign, err := db.ReadDesign(ctx, createdID)
	require.NoError(t, err, "Read failed after update")
	require.NotNil(t, updatedDesign)

	assert.Equal(t, "Updated Title", updatedDesign.Title.String)
	assert.Equal(t, usecases.VerticalStacked, *updatedDesign.FloorTilePattern)
	assert.Equal(t, usecases.Traditional, *updatedDesign.Style)
	assert.Equal(t, usecases.Bold, *updatedDesign.ColorScheme)
	assert.Equal(t, usecases.Archived, updatedDesign.Status)
	assert.True(t, updatedDesign.TubDoorVisible)
	assert.True(t, updatedDesign.NichesVisible)
	assert.Greater(t, updatedDesign.LastUpdated, updatedDesign.Created)
	assert.Greater(t, updatedDesign.LastUpdated, readDesign.LastUpdated)

	// --- 5. Test DesignsForProject ---
	// Create another design for the same project
	_, err = db.UpsertDesign(ctx, usecases.Design{
		ProjectID:          "PRJ-TEST123",
		Status:             usecases.Fave,
		WallpaperPlacement: usecases.AllWalls,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			FloorTilePattern: ptr(usecases.ThirdOffset),
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
				Mirror:    ptr(uuid.New()),
				Lighting:  ptr(uuid.New()),
				Paint:     ptr(uuid.New()),
			},
			Vanity: ptr(uuid.New()),
			Faucet: ptr(uuid.New()),
		},
	})
	require.NoError(t, err, "Create failed for second design")

	// Fetch both designs for the original project
	designs, err := db.DesignsForProject(ctx, "PRJ-TEST123")
	require.NoError(t, err, "Fetch designs failed")
	assert.Len(t, designs, 2, "Expected 2 designs for the project")

	// --- 6. Test DesignsByProject ---
	// Create another designs for a different project
	_, err = db.UpsertDesign(ctx, usecases.Design{
		ProjectID:          "PRJ-TEST456",
		WallpaperPlacement: usecases.VanityWall,
		WallTilePlacement:  usecases.FullWall,
		DesignOptions: usecases.DesignOptions{
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
				Mirror:    ptr(uuid.New()),
				Lighting:  ptr(uuid.New()),
				Paint:     ptr(uuid.New()),
			},
			FloorTilePattern: ptr(usecases.VerticalStacked),
			Vanity:           ptr(uuid.New()),
			Faucet:           ptr(uuid.New()),
		},
	})
	require.NoError(t, err, "Create failed for third design")

	// Fetch all designs for both projects
	projectDesigns, errors, err := db.DesignsByProject(ctx, []entities.ProjectId{"PRJ-TEST123", "PRJ-TEST456"})
	require.NoError(t, err, "Fetch designs by project failed")
	assert.Len(t, errors, 0, "Expected no errors")
	assert.Len(t, projectDesigns, 2, "Expected designs for 2 projects")

	// --- 7. Test Delete ---
	err = db.DeleteDesign(ctx, createdID)
	require.NoError(t, err, "Delete failed")

	// --- 8. Read again to verify delete ---
	_, err = db.ReadDesign(ctx, createdID)
	require.Error(t, err, "Read should fail for a deleted design")
	assert.Contains(t, err.Error(), "not found")
}

// TestRelationalDb_Integration_InsertPreset tests the InsertPreset method
// against a live database to ensure it correctly creates all related records.
func TestRelationalDb_Integration_InsertPreset(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE template.templates CASCADE;")
	require.NoError(t, err, "Failed to truncate templates table before test")
	_, err = testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate room_designs table before test")

	// First create a template that the preset can reference
	testTemplate := usecases.Template{
		ColorScheme:     usecases.Neutral,
		Style:           usecases.Modern,
		Name:            "Test Template for Preset",
		Description:     "A test template for preset testing",
		ImageURL:        url.URL{Scheme: "https", Host: "example.com", Path: "test.jpg"},
		Inspiration:     "Modern design",
		Atmosphere:      []string{"calm"},
		ColorPalette:    []string{"white"},
		MaterialPalette: []string{"marble"},
		HighlightedBrandUrls: []url.URL{
			{Scheme: "https", Host: "example.com", Path: "highlight1.jpg"},
			{Scheme: "https", Host: "example.com", Path: "highlight2.jpg"},
		},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile: ptr(uuid.New()),
			Lighting:  ptr(uuid.New()),
			Mirror:    ptr(uuid.New()),
			Paint:     ptr(uuid.New()),
			Shelving:  ptr(uuid.New()),
			Toilet:    ptr(uuid.New()),
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
			ShowerSystemCombo:  uuid.New(),
			ShowerSystemSolo:   uuid.New(),
			TubDoorFixed:       uuid.New(),
			TubDoorSliding:     uuid.New(),
		},
		ShowerFloorTile:    ptr(uuid.New()),
		ShowerWallTile:     ptr(uuid.New()),
		TubFiller:          ptr(uuid.New()),
		WallTilePlacement:  usecases.HalfWall,
		WallTile:           ptr(uuid.New()),
		WallpaperPlacement: usecases.NoWallpaper,
	}

	templateID, err := db.InsertTemplate(ctx, testTemplate, "00")
	require.NoError(t, err, "Failed to create test template")

	testPreset := usecases.Preset{
		Id:         "42",
		TemplateId: templateID,
		Design: usecases.Design{
			ID:                 uuid.New(),
			ProjectID:          "PRJ-PRESET-TEST",
			Status:             usecases.Preview,
			WallpaperPlacement: usecases.NoWallpaper,
			WallTilePlacement:  usecases.HalfWall,
			DesignOptions: usecases.DesignOptions{
				Title:            sql.NullString{String: "Test Preset Design", Valid: true},
				Description:      sql.NullString{String: "A test preset design", Valid: true},
				ColorScheme:      ptr(usecases.Neutral),
				Style:            ptr(usecases.Modern),
				FloorTilePattern: ptr(usecases.Herringbone),
				FixedProductSelections: usecases.FixedProductSelections{
					FloorTile: ptr(uuid.New()),
					Lighting:  ptr(uuid.New()),
					Mirror:    ptr(uuid.New()),
					Paint:     ptr(uuid.New()),
					Toilet:    ptr(uuid.New()),
				},
				Faucet: ptr(uuid.New()),
				Vanity: ptr(uuid.New()),
			},
			ShowerGlassVisible: true,
			TubDoorVisible:     false,
			NichesVisible:      true,
		},
		Rendition: entities.Rendition{
			Id:     uuid.New(),
			Status: entities.RenditionPending,
			URL:    url.URL{Scheme: "https", Host: "example.com", Path: "/image.webp"},
		},
	}

	// Act - Insert the preset
	err = db.InsertPreset(ctx, testPreset)
	if err != nil {
		log.Printf("Failed to insert preset with rendition image URL %v: %v", testPreset.Rendition.URL, err)
	}
	require.NoError(t, err, "InsertPreset failed")

	// Assert - Read the preset back to verify it was created correctly
	readPreset, err := db.FindPresetByLegacyId(ctx, testPreset.Id)
	require.NoError(t, err, "ReadPreset failed after insert")
	require.NotNil(t, readPreset)

	// Verify preset fields
	assert.Equal(t, testPreset.Id, readPreset.Id)

	// Verify design fields
	assert.NotEqual(t, uuid.Nil, readPreset.Design.ID)
	testPreset.Design.Created = readPreset.Design.Created
	testPreset.Design.LastUpdated = readPreset.Design.LastUpdated
	assert.Equal(t, testPreset.Design, readPreset.Design)

	// Verify rendition fields
	assert.NotEqual(t, uuid.Nil, readPreset.Rendition.Id)
	testPreset.Rendition.CreatedAt = readPreset.Rendition.CreatedAt
	testPreset.Rendition.UpdatedAt = readPreset.Rendition.UpdatedAt
	assert.Equal(t, testPreset.Rendition, readPreset.Rendition)
}

// TestRelationalDb_Integration_InsertTemplate tests the InsertTemplate method
// against a live database to ensure it correctly creates all related records.
func TestRelationalDb_Integration_InsertTemplate(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE template.templates CASCADE;")
	require.NoError(t, err, "Failed to truncate templates table before test")

	legacyId := "00"
	testTemplate := usecases.Template{
		LegacyId:        &legacyId,
		ColorScheme:     usecases.Neutral,
		Style:           usecases.Modern,
		Name:            "Test Template",
		Description:     "A test template for integration testing",
		ImageURL:        url.URL{Scheme: "https", Host: "example.com", Path: "/test.jpg"},
		Inspiration:     "Modern minimalist design",
		Atmosphere:      []string{"calm", "serene", "modern"},
		ColorPalette:    []string{"white", "gray", "black"},
		MaterialPalette: []string{"marble", "wood", "metal"},
		HighlightedBrandUrls: []url.URL{
			{Scheme: "https", Host: "example.com", Path: "/highlight1.jpg"},
			{Scheme: "https", Host: "example.com", Path: "/highlight2.jpg"},
		},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile: ptr(uuid.New()),
			Lighting:  ptr(uuid.New()),
			Mirror:    ptr(uuid.New()),
			Paint:     ptr(uuid.New()),
			Shelving:  ptr(uuid.New()),
			Toilet:    ptr(uuid.New()),
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
			ShowerSystemCombo:  uuid.New(),
			ShowerSystemSolo:   uuid.New(),
			TubDoorFixed:       uuid.New(),
			TubDoorSliding:     uuid.New(),
		},
		TemplateProvenance: usecases.TemplateProvenance{
			LightingBrand: ptr("Test Lighting Brand"),
			PlumbingBrand: ptr("Test Plumbing Brand"),
			ToiletBrand:   ptr("Test Toilet Brand"),
			VanityBrand:   ptr("Test Vanity Brand"),
			VanityStorage: ptr("Test Storage"),
		},
		ShowerFloorTile:    ptr(uuid.New()),
		ShowerWallTile:     ptr(uuid.New()),
		TubFiller:          ptr(uuid.New()),
		WallTilePlacement:  usecases.HalfWall,
		WallTile:           ptr(uuid.New()),
		WallpaperPlacement: usecases.VanityWall,
		Wallpaper:          ptr(uuid.New()),
		VanityScalingOptions: map[int]usecases.VanityScalingOption{
			36: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
			48: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
		},
	}

	// Act - Insert the template
	templateID, err := db.InsertTemplate(ctx, testTemplate, "00")
	require.NoError(t, err, "InsertTemplate failed")
	require.NotEqual(t, uuid.Nil, templateID, "InsertTemplate should return a valid UUID")

	// Assert - Read the template back to verify it was created correctly
	readTemplate, err := db.ReadTemplate(ctx, templateID)
	require.NoError(t, err, "ReadTemplate failed after insert")

	// Verify template fields
	assert.Equal(t, templateID, readTemplate.ID)
	testTemplate.ID = readTemplate.ID
	assert.Equal(t, legacyId, *readTemplate.LegacyId)
	testTemplate.LegacyId = readTemplate.LegacyId
	assert.NotZero(t, readTemplate.UpdatedAt)
	testTemplate.UpdatedAt = readTemplate.UpdatedAt
	assert.Equal(t, testTemplate, readTemplate)

	// --- Additional Test: ReadAllTemplates consistency ---
	// Verify that ReadAllTemplates returns the same template data as ReadTemplate
	allTemplates, err := db.ReadAllTemplates(ctx)
	require.NoError(t, err, "ReadAllTemplates failed")
	require.NotEmpty(t, allTemplates, "ReadAllTemplates should return at least one template")

	// Find our template in the results
	var foundTemplate *usecases.Template
	for i := range allTemplates {
		if allTemplates[i].ID == templateID {
			foundTemplate = &allTemplates[i]
			break
		}
	}
	require.NotNil(t, foundTemplate, "Template should be found in ReadAllTemplates results")

	// Verify that the template from ReadAllTemplates matches the one from ReadTemplate
	assert.Equal(t, readTemplate, *foundTemplate)
}

// TestRelationalDb_Integration_Renditions_CRUD performs a full create, read, update, and delete
// cycle for renditions against a live database to ensure the entire lifecycle works as expected.
func TestRelationalDb_Integration_Renditions_CRUD(t *testing.T) {
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate tables before test")

	// First create a design that the rendition can reference
	testDesign := usecases.Design{
		ProjectID:          "PRJ-RENDITION-TEST",
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			Title:            sql.NullString{String: "Rendition Test Design", Valid: true},
			Description:      sql.NullString{String: "A test design for rendition testing", Valid: true},
			ColorScheme:      ptr(usecases.Neutral),
			Style:            ptr(usecases.Modern),
			FloorTilePattern: ptr(usecases.Herringbone),
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Lighting:  ptr(uuid.New()),
				Mirror:    ptr(uuid.New()),
				Paint:     ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
			},
			Faucet: ptr(uuid.New()),
			Vanity: ptr(uuid.New()),
		},
	}

	designID, err := db.UpsertDesign(ctx, testDesign)
	require.NoError(t, err, "Failed to create test design")
	require.NotEqual(t, uuid.Nil, designID, "Design creation should return a valid UUID")

	testRendition := entities.Rendition{
		Id:     uuid.New(),
		Status: entities.RenditionPending,
		URL:    url.URL{Scheme: "https", Host: "example.com", Path: "/test-image.webp"},
	}

	// --- 1. Test Create ---
	createdID, err := db.InsertRendition(ctx, designID, testRendition)
	require.NoError(t, err, "Create rendition failed")
	require.NotEqual(t, uuid.Nil, createdID, "Create should return a valid UUID")
	assert.Equal(t, testRendition.Id, createdID, "Returned ID should match the provided ID")

	// --- 2. Test Read by ID ---
	readRenditions, err := db.Renditions(ctx, []uuid.UUID{createdID})
	require.NoError(t, err, "Read renditions by ID failed")
	require.Len(t, readRenditions, 1, "Should return exactly one rendition")

	readRendition := readRenditions[0]
	assert.Equal(t, createdID, readRendition.Id)
	assert.Equal(t, testRendition.Status, readRendition.Status)
	assert.Equal(t, testRendition.URL.String(), readRendition.URL.String())
	assert.NotZero(t, readRendition.CreatedAt)
	assert.NotZero(t, readRendition.UpdatedAt)
	assert.Equal(t, readRendition.CreatedAt, readRendition.UpdatedAt)

	// --- 3. Test Read by Design ID ---
	designRenditions, err := db.RenditionsForDesign(ctx, designID)
	require.NoError(t, err, "Read renditions by design ID failed")
	require.Len(t, designRenditions, 1, "Should return exactly one rendition for the design")
	assert.Equal(t, readRendition, designRenditions[0])

	// --- 4. Test Update ---
	newURL := url.URL{Scheme: "https", Host: "example.com", Path: "/updated-image.webp"}
	updateDiff := entities.RenditionDiff{
		Id:     createdID,
		Status: entities.RenditionCompleted,
		URL:    &newURL,
	}

	err = db.UpdateRendition(ctx, updateDiff)
	require.NoError(t, err, "Update rendition failed")

	// --- 5. Read again to verify update ---
	updatedRenditions, err := db.Renditions(ctx, []uuid.UUID{createdID})
	require.NoError(t, err, "Read renditions after update failed")
	require.Len(t, updatedRenditions, 1, "Should return exactly one rendition")

	updatedRendition := updatedRenditions[0]
	assert.Equal(t, createdID, updatedRendition.Id)
	assert.Equal(t, entities.RenditionCompleted, updatedRendition.Status)
	assert.Equal(t, newURL.String(), updatedRendition.URL.String())
	assert.Equal(t, readRendition.CreatedAt, updatedRendition.CreatedAt)
	assert.Greater(t, updatedRendition.UpdatedAt, updatedRendition.CreatedAt)

	// --- 6. Test Multiple Renditions for Same Design ---
	secondRendition := entities.Rendition{
		Id:     uuid.New(),
		Status: entities.RenditionStarted,
		URL:    url.URL{Scheme: "https", Host: "example.com", Path: "/second-image.webp"},
	}

	secondID, err := db.InsertRendition(ctx, designID, secondRendition)
	require.NoError(t, err, "Create second rendition failed")

	// Verify both renditions exist for the design
	allDesignRenditions, err := db.RenditionsForDesign(ctx, designID)
	require.NoError(t, err, "Read all renditions for design failed")
	assert.Len(t, allDesignRenditions, 2, "Should return two renditions for the design")

	// Verify we can read both by IDs
	bothRenditions, err := db.Renditions(ctx, []uuid.UUID{createdID, secondID})
	require.NoError(t, err, "Read multiple renditions by ID failed")
	assert.Len(t, bothRenditions, 2, "Should return both renditions")

	// --- 7. Test Delete ---
	err = db.DeleteRendition(ctx, createdID)
	require.NoError(t, err, "Delete rendition failed")

	// --- 8. Read again to verify delete ---
	deletedRenditions, err := db.Renditions(ctx, []uuid.UUID{createdID})
	require.NoError(t, err, "Read after delete should not error")
	assert.Len(t, deletedRenditions, 0, "Should return no renditions after delete")

	// Verify the second rendition still exists
	remainingRenditions, err := db.RenditionsForDesign(ctx, designID)
	require.NoError(t, err, "Read remaining renditions failed")
	assert.Len(t, remainingRenditions, 1, "Should have one remaining rendition")
	assert.Equal(t, secondID, remainingRenditions[0].Id)

	// --- 9. Test Delete Non-existent Rendition ---
	err = db.DeleteRendition(ctx, uuid.New())
	require.Error(t, err, "Delete non-existent rendition should fail")
	assert.Contains(t, err.Error(), "no rendition found")
}

// TestRelationalDb_Integration_Renditions_ValidationAndEdgeCases tests validation rules
// and edge cases for rendition operations.
func TestRelationalDb_Integration_Renditions_ValidationAndEdgeCases(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool)

	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate tables before test")

	testDesign := usecases.Design{
		ProjectID:          "PRJ-VALIDATION-TEST",
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			Title: sql.NullString{String: "Validation Test Design", Valid: true},
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
			},
		},
	}
	designID, err := db.UpsertDesign(ctx, testDesign)
	require.NoError(t, err, "Failed to create test design")

	// --- Test 1: Completed rendition without URL should fail ---
	invalidRendition := entities.Rendition{
		Id:     uuid.New(),
		Status: entities.RenditionCompleted,
		URL:    url.URL{}, // Empty URL
	}

	_, err = db.InsertRendition(ctx, designID, invalidRendition)
	require.Error(t, err, "Insert completed rendition without URL should fail")
	assert.Contains(t, err.Error(), "rendition URL cannot be empty")

	// --- Test 2: Pending rendition with valid URL should succeed ---
	validPendingRendition := entities.Rendition{
		Id:     uuid.New(),
		Status: entities.RenditionPending,
		URL:    url.URL{Scheme: "https", Host: "example.com", Path: "/pending-image.webp"},
	}

	pendingID, err := db.InsertRendition(ctx, designID, validPendingRendition)
	require.NoError(t, err, "Insert pending rendition should succeed")
	require.NotEqual(t, uuid.Nil, pendingID)

	// --- Test 3: Update to completed status without URL should fail ---
	invalidUpdate := entities.RenditionDiff{
		Id:     pendingID,
		Status: entities.RenditionCompleted,
		URL:    nil, // No URL provided
	}

	err = db.UpdateRendition(ctx, invalidUpdate)
	require.Error(t, err, "Update to completed without URL should fail")
	assert.Contains(t, err.Error(), "rendition URL cannot be empty")

	// --- Test 4: Update non-existent rendition should fail ---
	nonExistentUpdate := entities.RenditionDiff{
		Id:     uuid.New(),
		Status: entities.RenditionArchived,
	}

	err = db.UpdateRendition(ctx, nonExistentUpdate)
	require.Error(t, err, "Update non-existent rendition should fail")
	assert.Contains(t, err.Error(), "no rendition found")

	// --- Test 5: Insert rendition with nil UUID should generate new UUID ---
	renditionWithNilID := entities.Rendition{
		Id:     uuid.Nil,
		Status: entities.RenditionStarted,
		URL:    url.URL{Scheme: "https", Host: "example.com", Path: "/generated-id.webp"},
	}

	generatedID, err := db.InsertRendition(ctx, designID, renditionWithNilID)
	require.NoError(t, err, "Insert with nil UUID should succeed")
	require.NotEqual(t, uuid.Nil, generatedID, "Should generate a new UUID")

	// --- Test 6: Read empty list of rendition IDs ---
	emptyRenditions, err := db.Renditions(ctx, []uuid.UUID{})
	require.NoError(t, err, "Read empty list should not error")
	assert.Len(t, emptyRenditions, 0, "Should return empty list")

	// --- Test 7: Read renditions for non-existent design ---
	nonExistentDesignRenditions, err := db.RenditionsForDesign(ctx, uuid.New())
	require.NoError(t, err, "Read renditions for non-existent design should not error")
	assert.Len(t, nonExistentDesignRenditions, 0, "Should return empty list")

	// --- Test 8: Test all rendition statuses ---
	statuses := []entities.RenditionStatus{
		entities.RenditionPending,
		entities.RenditionStarted,
		entities.RenditionCompleted,
		entities.RenditionOutdated,
		entities.RenditionArchived,
	}

	var statusRenditionIDs []uuid.UUID
	for _, status := range statuses {
		rendition := entities.Rendition{
			Id:     uuid.New(),
			Status: status,
			URL: url.URL{
				Scheme: "https",
				Host:   "example.com",
				Path:   "/image-" + string(status) + ".webp",
			},
		}

		statusID, err := db.InsertRendition(ctx, designID, rendition)
		require.NoError(t, err, "Insert rendition with status %s should succeed", status)
		statusRenditionIDs = append(statusRenditionIDs, statusID)
	}

	// Verify all status renditions were created
	allStatusRenditions, err := db.Renditions(ctx, statusRenditionIDs)
	require.NoError(t, err, "Read all status renditions should succeed")
	assert.Len(t, allStatusRenditions, len(statuses), "Should return all status renditions")

	// Verify each status was preserved
	statusMap := make(map[uuid.UUID]entities.RenditionStatus)
	for _, rendition := range allStatusRenditions {
		statusMap[rendition.Id] = rendition.Status
	}

	for i, expectedStatus := range statuses {
		actualStatus, exists := statusMap[statusRenditionIDs[i]]
		require.True(t, exists, "Rendition with status %s should exist", expectedStatus)
		assert.Equal(t, expectedStatus, actualStatus, "Status should be preserved")
	}
}

// Helper function to get a pointer to a value
func ptr[T any](v T) *T {
	return &v
}
