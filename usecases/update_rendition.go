package usecases

import (
	"context"
	"errors"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type RenditionUpdater struct {
	renditionRepo renditionRepository
}

func NewRenditionUpdater(renditionRepo renditionRepository) *RenditionUpdater {
	if IsNil(renditionRepo) {
		panic("renditionRepo cannot be nil")
	}
	return &RenditionUpdater{renditionRepo: renditionRepo}
}

func (ru *RenditionUpdater) UpdateRendition(ctx context.Context,
	presenter OutcomePresenter, diff entities.RenditionDiff) {

	var zeroUUID uuid.UUID
	if diff.Id == zeroUUID || diff.Id == uuid.Nil {
		presenter.PresentError(ErrInvalidPayload)
		return
	}
	if diff.Status == entities.RenditionCompleted && diff.URL == nil {
		presenter.PresentError(errors.New("rendition URL cannot be empty when status is Completed"))
		return
	}
	if err := ru.renditionRepo.UpdateRendition(ctx, diff); err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.ConveySuccess()
}
