package usecases

import (
	"context"
	"log/slog"

	"github.com/google/uuid"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type BulkDesignSaver struct {
	logger     *slog.Logger
	designRepo designRepository
	monolith   monolith
}

func NewBulkDesignSaver(designRepo designRepository, monolith monolith, logger *slog.Logger) *BulkDesignSaver {
	if IsNil(designRepo) {
		panic("designRepo cannot be nil")
	}
	if IsNil(monolith) {
		panic("monolith cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &BulkDesignSaver{logger: logger, designRepo: designRepo, monolith: monolith}
}

func (bdr *BulkDesignSaver) SaveDesigns(ctx context.Context, presenter OutcomePresenter, designs []Design, projectId entities.ProjectId, newLeader uuid.UUID) {
	var zeroUUID uuid.UUID
	errors := []error{}
	for _, design := range designs {
		if design.ID == zeroUUID || design.ID == uuid.Nil {
			design.ID = uuid.New()
			design.Status = Preview
		}
		if _, err := bdr.designRepo.UpsertDesign(ctx, design); err != nil {
			bdr.logger.ErrorContext(ctx, "Failed to save design", slog.String("error", err.Error()))
			errors = append(errors, err)
		}
	}
	if len(errors) > 0 {
		presenter.PresentError(ErrInvalidPayload)
		return
	}
	if newLeader != uuid.Nil {
		bdr.monolith.UpdateCurrentDesignIdForProject(ctx, projectId, newLeader)
	}
	presenter.ConveySuccess()
}
