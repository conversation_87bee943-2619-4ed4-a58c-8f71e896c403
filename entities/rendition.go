package entities

import (
	"errors"
	"net/url"
	"time"

	"github.com/google/uuid"
)

var ErrRenditionIdMismatch = errors.New("rendition ID mismatch")

type RenditionStatus string

const (
	RenditionPending   RenditionStatus = "Pending"
	RenditionStarted   RenditionStatus = "Started"
	RenditionCompleted RenditionStatus = "Completed"
	RenditionOutdated  RenditionStatus = "Outdated"
	RenditionArchived  RenditionStatus = "Archived"
)

type Rendition struct {
	Id        uuid.UUID `json:"id"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`

	Status RenditionStatus `json:"status"`
	URL    url.URL         `json:"url"`
}

type RenditionDiff struct {
	Id     uuid.UUID       `json:"id"`
	Status RenditionStatus `json:"status"`
	URL    *url.URL        `json:"url"`
}

func (existing *Rendition) MergeWith(incoming RenditionDiff) error {
	if incoming.Id != existing.Id {
		return ErrRenditionIdMismatch
	}
	if incoming.Status == RenditionCompleted && incoming.URL == nil {
		return errors.New("rendition URL cannot be empty when status is Completed")
	}
	existing.Status = incoming.Status
	if incoming.URL != nil {
		existing.URL = *incoming.URL
	}
	return nil
}
